#!/usr/bin/env python3
"""
Create visualizations for the clustering analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.pipeline import Pipeline
from sklearn.metrics import silhouette_score, calinski_harabasz_score, davies_bouldin_score
from sklearn.manifold import TSNE
from kneed import KneeLocator
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

print("Creating visualizations for clustering analysis...")

# Load and prepare data
df = pd.read_csv('Clustered_Customer_Data.csv', index_col=0)
if 'Cluster' in df.columns:
    df = df.drop('Cluster', axis=1)

# Feature engineering (same as in main analysis)
df_features = df.copy()
df_features['TOTAL_SPENDING'] = (df_features['PURCHASES'] + df_features['CASH_ADVANCE'])
df_features['PURCHASE_TO_CREDIT_RATIO'] = df_features['PURCHASES'] / (df_features['CREDIT_LIMIT'] + 1e-8)
df_features['CASH_ADVANCE_TO_CREDIT_RATIO'] = df_features['CASH_ADVANCE'] / (df_features['CREDIT_LIMIT'] + 1e-8)
df_features['PAYMENT_TO_PURCHASE_RATIO'] = df_features['PAYMENTS'] / (df_features['PURCHASES'] + 1e-8)
df_features['AVG_PURCHASE_TRX_AMOUNT'] = df_features['PURCHASES'] / (df_features['PURCHASES_TRX'] + 1e-8)
df_features['BALANCE_TO_CREDIT_RATIO'] = df_features['BALANCE'] / (df_features['CREDIT_LIMIT'] + 1e-8)
df_features['MIN_PAYMENT_TO_BALANCE_RATIO'] = df_features['MINIMUM_PAYMENTS'] / (df_features['BALANCE'] + 1e-8)

clustering_features = [
    'BALANCE', 'BALANCE_FREQUENCY', 'PURCHASES', 'ONEOFF_PURCHASES',
    'INSTALLMENTS_PURCHASES', 'CASH_ADVANCE', 'PURCHASES_FREQUENCY',
    'ONEOFF_PURCHASES_FREQUENCY', 'PURCHASES_INSTALLMENTS_FREQUENCY',
    'CASH_ADVANCE_FREQUENCY', 'CASH_ADVANCE_TRX', 'PURCHASES_TRX',
    'CREDIT_LIMIT', 'PAYMENTS', 'MINIMUM_PAYMENTS', 'PRC_FULL_PAYMENT',
    'TENURE', 'TOTAL_SPENDING', 'PURCHASE_TO_CREDIT_RATIO',
    'CASH_ADVANCE_TO_CREDIT_RATIO', 'PAYMENT_TO_PURCHASE_RATIO',
    'AVG_PURCHASE_TRX_AMOUNT', 'BALANCE_TO_CREDIT_RATIO',
    'MIN_PAYMENT_TO_BALANCE_RATIO'
]

X = df_features[clustering_features].copy()
X = X.replace([np.inf, -np.inf], np.nan)
X = X.fillna(X.median())

# 1. Distribution plots
print("Creating distribution plots...")
fig, axes = plt.subplots(3, 3, figsize=(15, 12))
fig.suptitle('Distribution of Key Features', fontsize=16)

key_features = ['BALANCE', 'PURCHASES', 'CASH_ADVANCE', 'CREDIT_LIMIT', 
                'PAYMENTS', 'MINIMUM_PAYMENTS', 'PRC_FULL_PAYMENT', 
                'PURCHASES_FREQUENCY', 'TENURE']

for i, feature in enumerate(key_features):
    row = i // 3
    col = i % 3
    axes[row, col].hist(df[feature], bins=30, alpha=0.7, color='skyblue')
    axes[row, col].set_title(f'{feature}')
    axes[row, col].set_xlabel(feature)
    axes[row, col].set_ylabel('Frequency')

plt.tight_layout()
plt.savefig('feature_distributions.png', dpi=300, bbox_inches='tight')
plt.show()

# 2. Elbow Method Visualization
print("Creating elbow method plot...")
def elbow_method_plot(X, max_k=15):
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    inertias = []
    K_range = range(1, max_k + 1)
    
    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        kmeans.fit(X_scaled)
        inertias.append(kmeans.inertia_)
    
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(K_range, inertias, 'bo-', linewidth=2, markersize=8)
    plt.xlabel('Number of Clusters (k)')
    plt.ylabel('Inertia (WCSS)')
    plt.title('Elbow Method for Optimal k')
    plt.grid(True, alpha=0.3)
    
    # Use KneeLocator to find the elbow point
    kl = KneeLocator(K_range, inertias, curve='convex', direction='decreasing')
    optimal_k_elbow = kl.elbow
    
    if optimal_k_elbow:
        plt.axvline(x=optimal_k_elbow, color='red', linestyle='--', 
                   label=f'Optimal k = {optimal_k_elbow}')
        plt.legend()
    
    # Rate of change
    rate_of_change = []
    for i in range(1, len(inertias)):
        rate_of_change.append(inertias[i-1] - inertias[i])
    
    plt.subplot(1, 2, 2)
    plt.plot(K_range[1:], rate_of_change, 'ro-', linewidth=2, markersize=8)
    plt.xlabel('Number of Clusters (k)')
    plt.ylabel('Rate of Change in Inertia')
    plt.title('Rate of Change in Inertia')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('elbow_method.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return optimal_k_elbow

optimal_k = elbow_method_plot(X)

# 3. Silhouette Analysis
print("Creating silhouette analysis plot...")
def silhouette_analysis_plot(X, max_k=15):
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    silhouette_scores = []
    K_range = range(2, max_k + 1)
    
    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(X_scaled)
        silhouette_avg = silhouette_score(X_scaled, cluster_labels)
        silhouette_scores.append(silhouette_avg)
    
    plt.figure(figsize=(10, 6))
    plt.plot(K_range, silhouette_scores, 'go-', linewidth=2, markersize=8)
    plt.xlabel('Number of Clusters (k)')
    plt.ylabel('Average Silhouette Score')
    plt.title('Silhouette Analysis for Optimal k')
    plt.grid(True, alpha=0.3)
    
    optimal_k_silhouette = K_range[np.argmax(silhouette_scores)]
    plt.axvline(x=optimal_k_silhouette, color='red', linestyle='--', 
               label=f'Optimal k = {optimal_k_silhouette}')
    plt.legend()
    
    plt.savefig('silhouette_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return optimal_k_silhouette

silhouette_k = silhouette_analysis_plot(X)

# 4. Additional Metrics
print("Creating additional metrics plots...")
def additional_metrics_plot(X, max_k=15):
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    ch_scores = []
    db_scores = []
    K_range = range(2, max_k + 1)
    
    for k in K_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(X_scaled)
        
        ch_score = calinski_harabasz_score(X_scaled, cluster_labels)
        db_score = davies_bouldin_score(X_scaled, cluster_labels)
        
        ch_scores.append(ch_score)
        db_scores.append(db_score)
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Calinski-Harabasz Index
    ax1.plot(K_range, ch_scores, 'bo-', linewidth=2, markersize=8)
    ax1.set_xlabel('Number of Clusters (k)')
    ax1.set_ylabel('Calinski-Harabasz Index')
    ax1.set_title('Calinski-Harabasz Index (Higher is Better)')
    ax1.grid(True, alpha=0.3)
    
    optimal_k_ch = K_range[np.argmax(ch_scores)]
    ax1.axvline(x=optimal_k_ch, color='red', linestyle='--', 
               label=f'Optimal k = {optimal_k_ch}')
    ax1.legend()
    
    # Davies-Bouldin Index
    ax2.plot(K_range, db_scores, 'ro-', linewidth=2, markersize=8)
    ax2.set_xlabel('Number of Clusters (k)')
    ax2.set_ylabel('Davies-Bouldin Index')
    ax2.set_title('Davies-Bouldin Index (Lower is Better)')
    ax2.grid(True, alpha=0.3)
    
    optimal_k_db = K_range[np.argmin(db_scores)]
    ax2.axvline(x=optimal_k_db, color='red', linestyle='--', 
               label=f'Optimal k = {optimal_k_db}')
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig('additional_metrics.png', dpi=300, bbox_inches='tight')
    plt.show()

additional_metrics_plot(X)

# 5. Final clustering with best pipeline
print("Creating final clustering visualizations...")
final_k = 3
pipeline = Pipeline([
    ('scaler', RobustScaler()),
    ('kmeans', KMeans(n_clusters=final_k, random_state=42, n_init=10))
])

cluster_labels = pipeline.fit_predict(X)

# 2D PCA Visualization
pca_2d = PCA(n_components=2)
X_pca_2d = pca_2d.fit_transform(StandardScaler().fit_transform(X))

plt.figure(figsize=(12, 8))
unique_labels = np.unique(cluster_labels)
colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

for i, label in enumerate(unique_labels):
    mask = cluster_labels == label
    plt.scatter(X_pca_2d[mask, 0], X_pca_2d[mask, 1], 
               c=[colors[i]], label=f'Cluster {label}', 
               alpha=0.7, s=50)

plt.xlabel(f'First Principal Component ({pca_2d.explained_variance_ratio_[0]:.2%} variance)')
plt.ylabel(f'Second Principal Component ({pca_2d.explained_variance_ratio_[1]:.2%} variance)')
plt.title('2D PCA Cluster Visualization')
plt.legend()
plt.grid(True, alpha=0.3)
plt.savefig('2d_pca_clusters.png', dpi=300, bbox_inches='tight')
plt.show()

# 3D PCA Visualization
pca_3d = PCA(n_components=3)
X_pca_3d = pca_3d.fit_transform(StandardScaler().fit_transform(X))

fig = plt.figure(figsize=(12, 9))
ax = fig.add_subplot(111, projection='3d')

for i, label in enumerate(unique_labels):
    mask = cluster_labels == label
    ax.scatter(X_pca_3d[mask, 0], X_pca_3d[mask, 1], X_pca_3d[mask, 2],
              c=[colors[i]], label=f'Cluster {label}', 
              alpha=0.7, s=50)

ax.set_xlabel(f'PC1 ({pca_3d.explained_variance_ratio_[0]:.2%})')
ax.set_ylabel(f'PC2 ({pca_3d.explained_variance_ratio_[1]:.2%})')
ax.set_zlabel(f'PC3 ({pca_3d.explained_variance_ratio_[2]:.2%})')
ax.set_title('3D PCA Cluster Visualization')
ax.legend()
plt.savefig('3d_pca_clusters.png', dpi=300, bbox_inches='tight')
plt.show()

# Cluster characteristics heatmap
print("Creating cluster characteristics heatmap...")
df_analysis = X.copy()
df_analysis['Cluster'] = cluster_labels
cluster_means = df_analysis.groupby('Cluster').mean()

plt.figure(figsize=(20, 8))
sns.heatmap(cluster_means.T, annot=True, cmap='coolwarm', center=0, 
            fmt='.2f', cbar_kws={"shrink": .8})
plt.title('Cluster Characteristics Heatmap', fontsize=16)
plt.xlabel('Cluster')
plt.ylabel('Features')
plt.xticks(rotation=0)
plt.yticks(rotation=0)
plt.tight_layout()
plt.savefig('cluster_heatmap.png', dpi=300, bbox_inches='tight')
plt.show()

# Cluster size pie chart
unique_labels, counts = np.unique(cluster_labels, return_counts=True)
cluster_sizes = dict(zip(unique_labels, counts))

plt.figure(figsize=(10, 8))
labels = [f'Cluster {i}\n({counts[i]} customers)' for i in unique_labels]
sizes = [cluster_sizes[i] for i in unique_labels]
colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

plt.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=90, colors=colors)
plt.title('Cluster Size Distribution', fontsize=16)
plt.axis('equal')
plt.savefig('cluster_sizes.png', dpi=300, bbox_inches='tight')
plt.show()

# Feature importance (variance across clusters)
feature_variance = cluster_means.var().sort_values(ascending=False)

plt.figure(figsize=(12, 8))
plt.barh(range(len(feature_variance)), feature_variance.values)
plt.yticks(range(len(feature_variance)), 
           [name[:20] + '...' if len(name) > 20 else name for name in feature_variance.index])
plt.xlabel('Variance Across Clusters')
plt.title('Feature Importance (Variance Across Clusters)')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
plt.show()

print("All visualizations have been created and saved!")
print("Generated files:")
print("- feature_distributions.png")
print("- elbow_method.png") 
print("- silhouette_analysis.png")
print("- additional_metrics.png")
print("- 2d_pca_clusters.png")
print("- 3d_pca_clusters.png")
print("- cluster_heatmap.png")
print("- cluster_sizes.png")
print("- feature_importance.png")
